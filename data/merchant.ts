import {
  Merchant,
  GroupPurchase,
  PaymentTracking,
  ManufacturingUpdate,
  InstallationTask,
  TeamMember,
  MerchantInventoryItem,
  ManufacturingPhase,
  ProductCategory,
} from "@/types/merchant";
import {
  generateSAName,
  generateSAAddress,
  generateSAPhoneNumber,
  generateCemeteryAddress,
  generateBusinessRegistration,
  generateTaxNumber,
  convertToZAR,
  formatZAR,
} from "@/data/south-african-context";
import { getStoneImagesForCategory } from "@/lib/stone-images";

// Mock categories
export const mockCategories: ProductCategory[] = [
  {
    id: "cat1",
    merchantId: "m1",
    name: "Traditional Memorials",
    description: "Classic granite memorial stones with timeless designs",
    slug: "traditional",
    color: "#8B5A3C",
    icon: "monument",
    isActive: true,
    productCount: 8,
    displayOrder: 1,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-15"),
  },
  {
    id: "cat2",
    merchantId: "m1",
    name: "Modern Memorials",
    description: "Contemporary memorial designs with clean lines",
    slug: "modern",
    color: "#2563EB",
    icon: "square",
    isActive: true,
    productCount: 5,
    displayOrder: 2,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-10"),
  },
  {
    id: "cat3",
    merchantId: "m1",
    name: "Custom Designs",
    description: "Personalized memorial stones with custom engraving",
    slug: "custom",
    color: "#7C3AED",
    icon: "star",
    isActive: true,
    productCount: 12,
    displayOrder: 3,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-20"),
  },
  {
    id: "cat4",
    merchantId: "m1",
    name: "Premium Granite",
    description: "High-end granite memorials with premium finishes",
    slug: "premium",
    color: "#DC2626",
    icon: "gem",
    isActive: true,
    productCount: 3,
    displayOrder: 4,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-05"),
  },
  {
    id: "cat5",
    merchantId: "m1",
    name: "Compact Memorials",
    description: "Smaller memorial plaques for limited spaces",
    slug: "compact",
    color: "#059669",
    icon: "minimize",
    isActive: false,
    productCount: 2,
    displayOrder: 5,
    createdAt: new Date("2023-12-15"),
    updatedAt: new Date("2024-01-01"),
  },
];

// Mock merchant data - South African context
export const mockMerchant: Merchant = {
  id: "m1",
  name: "Cape Stone Memorials",
  email: "<EMAIL>",
  phone: generateSAPhoneNumber(),
  logo: "/images/merchants/cape-stone.png",
  rating: 4.8,
  totalProducts: 12,
  totalSales: convertToZAR(125000),
  joinedAt: new Date("2023-01-15"),
  status: "active",
  businessDetails: {
    businessName: "Cape Stone Memorials (Pty) Ltd",
    businessAddress: generateSAAddress(),
    taxId: generateTaxNumber(),
    businessType: "Memorial Stone Manufacturing",
  },
};

// Mock groups purchasing from this merchant - South African context with product images
export const mockGroups: (GroupPurchase & {
  productName: string;
  productImage: string;
  category: string;
})[] = [
  {
    id: "g1",
    productId: "p1",
    merchantId: "m1",
    groupName: `Memorial for ${generateSAName().fullName}`,
    adminId: "u1",
    totalMembers: 8,
    targetAmount: convertToZAR(3200),
    currentAmount: convertToZAR(2880),
    paymentProgress: 90,
    status: "collecting",
    createdAt: new Date("2024-01-15"),
    productName: "Classic Granite Memorial Stone",
    productImage: getStoneImagesForCategory("traditional", 1)[0],
    category: "traditional",
  },
  {
    id: "g2",
    productId: "p2",
    merchantId: "m1",
    groupName: `${generateSAName().lastName} Family Heritage Stone`,
    adminId: "u2",
    totalMembers: 12,
    targetAmount: convertToZAR(4500),
    currentAmount: convertToZAR(3600),
    paymentProgress: 80,
    status: "manufacturing",
    createdAt: new Date("2024-01-10"),
    manufacturingStarted: new Date("2024-01-20"),
    productName: "Modern Minimalist Memorial",
    productImage: getStoneImagesForCategory("modern", 1)[0],
    category: "modern",
  },
  {
    id: "g3",
    productId: "p3",
    merchantId: "m1",
    groupName: `Custom Memorial for ${generateSAName().fullName}`,
    adminId: "u3",
    totalMembers: 6,
    targetAmount: convertToZAR(5200),
    currentAmount: convertToZAR(2600),
    paymentProgress: 50,
    status: "discussing",
    createdAt: new Date("2024-01-20"),
    productName: "Custom Engraved Memorial Stone",
    productImage: getStoneImagesForCategory("custom", 1)[0],
    category: "custom",
  },
  {
    id: "g4",
    productId: "p4",
    merchantId: "m1",
    groupName: `Premium Memorial for ${generateSAName().fullName}`,
    adminId: "u4",
    totalMembers: 15,
    targetAmount: convertToZAR(8500),
    currentAmount: convertToZAR(7650),
    paymentProgress: 90,
    status: "installing",
    createdAt: new Date("2024-01-05"),
    manufacturingStarted: new Date("2024-01-15"),
    manufacturingCompleted: new Date("2024-02-05"),
    productName: "Premium Granite Monument",
    productImage: getStoneImagesForCategory("premium", 1)[0],
    category: "premium",
  },
];

// Mock team members - South African context
export const mockTeamMembers: TeamMember[] = [
  {
    id: "t1",
    merchantId: "m1",
    name: generateSAName().fullName,
    email: "<EMAIL>",
    phone: generateSAPhoneNumber(),
    role: "contractor",
    specialties: ["Stone Installation", "Foundation Work", "Heavy Equipment"],
    isActive: true,
    bankingDetails: {
      accountNumber: "****1234",
      routingNumber: "632005",
      bankName: "Standard Bank",
      accountHolderName: "Pieter van der Merwe",
    },
    identification: {
      idType: "national_id",
      idNumber: "*************",
      idImageUrl: "/images/id-pieter.jpg",
    },
    joinedAt: new Date("2023-06-01"),
  },
  {
    id: "t2",
    merchantId: "m1",
    name: generateSAName().fullName,
    email: "<EMAIL>",
    phone: generateSAPhoneNumber(),
    role: "contractor",
    specialties: ["Monument Installation", "Landscaping", "Site Preparation"],
    isActive: true,
    bankingDetails: {
      accountNumber: "****5678",
      routingNumber: "632005",
      bankName: "Standard Bank",
      accountHolderName: "Nomsa Mthembu",
    },
    identification: {
      idType: "national_id",
      idNumber: "*************",
      idImageUrl: "/images/id-nomsa.jpg",
    },
    joinedAt: new Date("2023-08-15"),
  },
  {
    id: "t3",
    merchantId: "m1",
    name: generateSAName().fullName,
    email: "<EMAIL>",
    phone: generateSAPhoneNumber(),
    role: "employee",
    specialties: ["Equipment Operation", "Site Cleanup", "Transport"],
    isActive: true,
    bankingDetails: {
      accountNumber: "****9012",
      routingNumber: "470010",
      bankName: "FNB",
      accountHolderName: "Thabo Mokoena",
    },
    identification: {
      idType: "national_id",
      idNumber: "*************",
      idImageUrl: "/images/id-thabo.jpg",
    },
    joinedAt: new Date("2023-09-01"),
  },
];

// Mock manufacturing phases
export const mockManufacturingPhases: ManufacturingPhase[] = [
  {
    id: "ph1",
    productId: "p1",
    name: "Design Approval",
    description: "Customer approves final design and specifications",
    estimatedDuration: 2,
    order: 1,
    isRequired: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
    isGlobal: false,
    merchantId: "m1",
  },
  {
    id: "ph2",
    productId: "p1",
    name: "Stone Selection & Preparation",
    description: "Select and prepare granite stone for cutting",
    estimatedDuration: 3,
    order: 2,
    isRequired: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
    isGlobal: false,
    merchantId: "m1",
  },
  {
    id: "ph3",
    productId: "p1",
    name: "Stone Cutting",
    description: "Cut stone to specified dimensions and shape",
    estimatedDuration: 5,
    order: 3,
    isRequired: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
    isGlobal: false,
    merchantId: "m1",
  },
  {
    id: "ph4",
    productId: "p1",
    name: "Engraving",
    description: "Engrave text and designs onto the stone",
    estimatedDuration: 7,
    order: 4,
    isRequired: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
    isGlobal: false,
    merchantId: "m1",
  },
  {
    id: "ph5",
    productId: "p1",
    name: "Final Polish",
    description: "Polish and finish the memorial stone",
    estimatedDuration: 4,
    order: 5,
    isRequired: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
    isGlobal: false,
    merchantId: "m1",
  },
];

// Mock installation tasks - South African context with product images
export const mockInstallations: (InstallationTask & {
  productName: string;
  productImage: string;
  category: string;
  groupName: string;
})[] = [
  {
    id: "inst1",
    groupId: "g1",
    productId: "p1",
    merchantId: "m1",
    title: "Memorial Installation - Stellenbosch Cemetery",
    description: `Installation of granite memorial stone for the ${
      generateSAName().lastName
    } family`,
    installationAddress: generateCemeteryAddress(),
    scheduledDate: new Date("2024-02-15"),
    estimatedDuration: 4,
    assignedTeam: ["t1", "t2"],
    teamLeadId: "t1",
    status: "scheduled",
    paymentReleased: false,
    createdAt: new Date("2024-01-25"),
    updatedAt: new Date("2024-01-25"),
    productName: "Classic Granite Memorial Stone",
    productImage: getStoneImagesForCategory("traditional", 1)[0],
    category: "traditional",
    groupName: `Memorial for ${generateSAName().fullName}`,
  },
  {
    id: "inst2",
    groupId: "g2",
    productId: "p2",
    merchantId: "m1",
    title: "Heritage Stone Installation - Paarl Cemetery",
    description: "Installation of modern memorial stone",
    installationAddress: generateCemeteryAddress(),
    scheduledDate: new Date("2024-02-20"),
    estimatedDuration: 6,
    assignedTeam: ["t1", "t2"],
    teamLeadId: "t1",
    status: "scheduled",
    paymentReleased: false,
    createdAt: new Date("2024-01-28"),
    updatedAt: new Date("2024-01-28"),
    productName: "Modern Minimalist Memorial",
    productImage: getStoneImagesForCategory("modern", 1)[0],
    category: "modern",
    groupName: `${generateSAName().lastName} Family Heritage Stone`,
  },
  {
    id: "inst3",
    groupId: "g4",
    productId: "p4",
    merchantId: "m1",
    title: "Premium Installation - Durbanville Memorial Park",
    description: "Installation of premium granite monument",
    installationAddress: generateCemeteryAddress(),
    scheduledDate: new Date("2024-02-25"),
    estimatedDuration: 8,
    assignedTeam: ["t1", "t2", "t3"],
    teamLeadId: "t1",
    status: "in_progress",
    paymentReleased: true,
    createdAt: new Date("2024-02-01"),
    updatedAt: new Date("2024-02-10"),
    productName: "Premium Granite Monument",
    productImage: getStoneImagesForCategory("premium", 1)[0],
    category: "premium",
    groupName: `Premium Memorial for ${generateSAName().fullName}`,
  },
];

// Helper functions for merchant operations
export const getMerchantGroups = (merchantId: string): GroupPurchase[] => {
  return mockGroups.filter((group) => group.merchantId === merchantId);
};

export const getMerchantTeam = (merchantId: string): TeamMember[] => {
  return mockTeamMembers.filter((member) => member.merchantId === merchantId);
};

export const getMerchantInstallations = (
  merchantId: string
): InstallationTask[] => {
  return mockInstallations.filter(
    (installation) => installation.merchantId === merchantId
  );
};

export const getGroupsByStatus = (
  merchantId: string,
  status: GroupPurchase["status"]
): GroupPurchase[] => {
  return getMerchantGroups(merchantId).filter(
    (group) => group.status === status
  );
};

export const getReadyForManufacturing = (
  merchantId: string
): GroupPurchase[] => {
  return getMerchantGroups(merchantId).filter(
    (group) => group.paymentProgress >= 80 && group.status === "collecting"
  );
};

export const getActiveInstallations = (
  merchantId: string
): InstallationTask[] => {
  return getMerchantInstallations(merchantId).filter(
    (installation) =>
      installation.status === "scheduled" ||
      installation.status === "in_progress"
  );
};

export const getTeamMembersByRole = (
  merchantId: string,
  role: TeamMember["role"]
): TeamMember[] => {
  return getMerchantTeam(merchantId).filter(
    (member) => member.role === role && member.isActive
  );
};

// Category helper functions
export const getMerchantCategories = (
  merchantId: string
): ProductCategory[] => {
  return mockCategories.filter(
    (category) => category.merchantId === merchantId
  );
};

export const getActiveCategories = (merchantId: string): ProductCategory[] => {
  return getMerchantCategories(merchantId).filter(
    (category) => category.isActive
  );
};

export const getCategoryBySlug = (
  merchantId: string,
  slug: string
): ProductCategory | undefined => {
  return getMerchantCategories(merchantId).find(
    (category) => category.slug === slug
  );
};

export const getCategoryById = (
  merchantId: string,
  categoryId: string
): ProductCategory | undefined => {
  return getMerchantCategories(merchantId).find(
    (category) => category.id === categoryId
  );
};
