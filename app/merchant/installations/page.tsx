"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Search,
  Plus,
  Calendar,
  MapPin,
  Users,
  Clock,
  CheckCircle,
  AlertCircle,
  Eye,
  Edit,
  UserCheck,
  DollarSign,
} from "lucide-react";
import { InstallationTask, TeamMember } from "@/types/merchant";
import { getStoneImagesForCategory } from "@/lib/stone-images";
import Image from "next/image";

// South African installation data with product images
const mockInstallations: (InstallationTask & {
  productName: string;
  productImage: string;
  category: string;
})[] = [
  {
    id: "inst1",
    groupId: "g1",
    productId: "p1",
    merchantId: "m1",
    title: "Memorial Installation - Westpark Cemetery",
    description: "Installation of granite memorial stone for the Smith family",
    installationAddress:
      "Westpark Cemetery, Section B, Plot 45, Johannesburg, GP",
    scheduledDate: new Date("2024-02-15"),
    estimatedDuration: 4,
    assignedTeam: ["t1", "t2"],
    teamLeadId: "t1",
    status: "scheduled",
    paymentReleased: false,
    createdAt: new Date("2024-01-25"),
    updatedAt: new Date("2024-01-25"),
    productName: "Classic Granite Memorial",
    productImage: getStoneImagesForCategory("traditional", 1)[0],
    category: "traditional",
  },
  {
    id: "inst2",
    groupId: "g2",
    productId: "p2",
    merchantId: "m1",
    title: "Heritage Stone Installation - Braamfontein Cemetery",
    description: "Installation of custom heritage memorial stone",
    installationAddress:
      "Braamfontein Cemetery, Garden Section, Johannesburg, GP",
    scheduledDate: new Date("2024-02-20"),
    estimatedDuration: 6,
    assignedTeam: ["t1", "t3", "t4"],
    teamLeadId: "t1",
    status: "scheduled",
    paymentReleased: false,
    createdAt: new Date("2024-01-28"),
    updatedAt: new Date("2024-01-28"),
    productName: "Heritage Family Stone",
    productImage: getStoneImagesForCategory("premium", 1)[0],
    category: "premium",
  },
  {
    id: "inst3",
    groupId: "g3",
    productId: "p3",
    merchantId: "m1",
    title: "Premium Monument Installation - Roodepoort Cemetery",
    description:
      "Installation of premium granite monument with foundation work",
    installationAddress: "Roodepoort Cemetery, Premium Section, Roodepoort, GP",
    scheduledDate: new Date("2024-02-10"),
    estimatedDuration: 8,
    assignedTeam: ["t2", "t3", "t4", "t5"],
    teamLeadId: "t2",
    status: "in_progress",
    paymentReleased: false,
    createdAt: new Date("2024-01-20"),
    updatedAt: new Date("2024-02-08"),
    productName: "Premium Granite Monument",
    productImage: getStoneImagesForCategory("custom", 1)[0],
    category: "custom",
  },
  {
    id: "inst4",
    groupId: "g4",
    productId: "p1",
    merchantId: "m1",
    title: "Community Memorial - Emmarentia Dam",
    description: "Installation of community memorial stone in park setting",
    installationAddress:
      "Emmarentia Memorial Park, Community Section, Johannesburg, GP",
    scheduledDate: new Date("2024-02-05"),
    estimatedDuration: 3,
    assignedTeam: ["t1", "t2"],
    teamLeadId: "t1",
    status: "pending_approval",
    completionImages: [
      getStoneImagesForCategory("modern", 2)[0],
      getStoneImagesForCategory("modern", 2)[1],
    ],
    completionNotes:
      "Installation completed successfully. All specifications met.",
    customerApproval: {
      approved: false,
      approvedBy: "",
      approvedAt: new Date(),
      feedback: "",
    },
    paymentReleased: false,
    createdAt: new Date("2024-01-15"),
    updatedAt: new Date("2024-02-05"),
    productName: "Community Memorial Stone",
    productImage: getStoneImagesForCategory("modern", 1)[0],
    category: "modern",
  },
  {
    id: "inst5",
    groupId: "g5",
    productId: "p4",
    merchantId: "m1",
    title: "Estate Monument - Sandton Memorial Gardens",
    description: "Installation of luxury estate monument with landscaping",
    installationAddress:
      "Sandton Memorial Gardens, Estate Section, Sandton, GP",
    scheduledDate: new Date("2024-01-30"),
    estimatedDuration: 12,
    assignedTeam: ["t2", "t3", "t4", "t5", "t6"],
    teamLeadId: "t2",
    status: "completed",
    completionImages: [getStoneImagesForCategory("premium", 2)[1]],
    completionNotes:
      "Estate monument installation completed with full landscaping.",
    customerApproval: {
      approved: true,
      approvedBy: "u5",
      approvedAt: new Date("2024-02-02"),
      feedback: "Excellent work, exceeded expectations.",
    },
    paymentReleased: true,
    createdAt: new Date("2024-01-10"),
    updatedAt: new Date("2024-02-02"),
    productName: "Estate Monument",
    productImage: getStoneImagesForCategory("premium", 1)[0],
    category: "premium",
  },
];

const mockTeamMembers: TeamMember[] = [
  {
    id: "t1",
    merchantId: "m1",
    name: "John Martinez",
    email: "<EMAIL>",
    phone: "+27 82 123 4567",
    photoUrl: "/images/team/john-martinez.jpg",
    role: "contractor",
    specialties: ["Stone Installation", "Foundation Work"],
    isActive: true,
    isVerified: true,
    joinedAt: new Date("2023-06-01"),
  },
  {
    id: "t2",
    merchantId: "m1",
    name: "Sarah Johnson",
    email: "<EMAIL>",
    phone: "+27 82 123 45678",
    photoUrl: "/images/team/sarah-johnson.jpg",
    role: "contractor",
    specialties: ["Monument Installation", "Landscaping"],
    isActive: true,
    isVerified: true,
    joinedAt: new Date("2023-08-15"),
  },
  {
    id: "t3",
    merchantId: "m1",
    name: "Mike Chen",
    email: "<EMAIL>",
    phone: "+27 82 123 4567",
    photoUrl: "/images/team/mike-chen.jpg",
    role: "employee",
    specialties: ["Equipment Operation", "Site Preparation"],
    isActive: true,
    isVerified: true,
    joinedAt: new Date("2023-09-01"),
  },
];

export default function MerchantInstallations() {
  const [installations, setInstallations] =
    useState<InstallationTask[]>(mockInstallations);
  const [filteredInstallations, setFilteredInstallations] =
    useState<InstallationTask[]>(mockInstallations);
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>(mockTeamMembers);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");

  useEffect(() => {
    let filtered = installations;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (installation) =>
          installation.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          installation.installationAddress
            .toLowerCase()
            .includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(
        (installation) => installation.status === statusFilter
      );
    }

    setFilteredInstallations(filtered);
  }, [installations, searchTerm, statusFilter]);

  const getStatusColor = (status: InstallationTask["status"]) => {
    switch (status) {
      case "scheduled":
        return "bg-blue-100 text-blue-800";
      case "in_progress":
        return "bg-yellow-100 text-yellow-800";
      case "completed":
        return "bg-green-100 text-green-800";
      case "pending_approval":
        return "bg-orange-100 text-orange-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: InstallationTask["status"]) => {
    switch (status) {
      case "scheduled":
        return <Calendar className="h-4 w-4" />;
      case "in_progress":
        return <Clock className="h-4 w-4" />;
      case "completed":
        return <CheckCircle className="h-4 w-4" />;
      case "pending_approval":
        return <AlertCircle className="h-4 w-4" />;
      case "cancelled":
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getTeamMemberName = (memberId: string) => {
    const member = teamMembers.find((m) => m.id === memberId);
    return member ? member.name : "Unknown";
  };

  const getTeamMemberPhoto = (memberId: string) => {
    const member = teamMembers.find((m) => m.id === memberId);
    return member?.photoUrl || "";
  };

  // Calculate summary statistics
  const scheduled = filteredInstallations.filter(
    (i) => i.status === "scheduled"
  ).length;
  const inProgress = filteredInstallations.filter(
    (i) => i.status === "in_progress"
  ).length;
  const pendingApproval = filteredInstallations.filter(
    (i) => i.status === "pending_approval"
  ).length;
  const completed = filteredInstallations.filter(
    (i) => i.status === "completed"
  ).length;
  const paymentsReleased = filteredInstallations.filter(
    (i) => i.paymentReleased
  ).length;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Installations</h1>
          <p className="text-muted-foreground">
            Manage installation tasks and team assignments
          </p>
        </div>
        <Link href="/merchant/installations/new">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Schedule Installation
          </Button>
        </Link>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Scheduled</CardTitle>
            <Calendar className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{scheduled}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <Clock className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{inProgress}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Pending Approval
            </CardTitle>
            <AlertCircle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingApproval}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completed}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Payments Released
            </CardTitle>
            <DollarSign className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{paymentsReleased}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search installations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="scheduled">Scheduled</SelectItem>
                <SelectItem value="in_progress">In Progress</SelectItem>
                <SelectItem value="pending_approval">
                  Pending Approval
                </SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Installations List */}
      <div className="space-y-4">
        {filteredInstallations.map((installation) => (
          <Card key={installation.id}>
            <CardContent className="p-6">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <Image
                    src={
                      installation.productImage ||
                      "/images/products/placeholder.jpg"
                    }
                    alt={installation.productName || "Product image"}
                    width={80}
                    height={80}
                    className="rounded-lg object-cover"
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div className="space-y-3 flex-1">
                      <div className="flex items-center space-x-3">
                        <div>
                          <h3 className="text-lg font-semibold">
                            {installation.title}
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            {installation.productName}
                          </p>
                        </div>
                        <Badge className={getStatusColor(installation.status)}>
                          <div className="flex items-center space-x-1">
                            {getStatusIcon(installation.status)}
                            <span className="capitalize">
                              {installation.status.replace("_", " ")}
                            </span>
                          </div>
                        </Badge>
                        {installation.status === "pending_approval" && (
                          <Badge
                            variant="outline"
                            className="text-orange-600 border-orange-600"
                          >
                            Awaiting Customer Approval
                          </Badge>
                        )}
                      </div>

                      <p className="text-muted-foreground">
                        {installation.description}
                      </p>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">
                            {installation.scheduledDate.toLocaleDateString(
                              "en-ZA"
                            )}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">
                            {installation.estimatedDuration} hours
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <MapPin className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm truncate">
                            {installation.installationAddress.split(",")[0]}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Users className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">
                            {installation.assignedTeam.length} team members
                          </span>
                        </div>
                      </div>

                      {/* Team Assignment */}
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <UserCheck className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm font-medium">
                            Team Lead:{" "}
                            {getTeamMemberName(installation.teamLeadId)}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-muted-foreground">
                            Team:
                          </span>
                          <div className="flex -space-x-2">
                            {installation.assignedTeam
                              .slice(0, 3)
                              .map((memberId) => (
                                <Avatar
                                  key={memberId}
                                  className="h-6 w-6 border-2 border-background"
                                >
                                  <AvatarImage
                                    src={getTeamMemberPhoto(memberId)}
                                    alt={getTeamMemberName(memberId)}
                                  />
                                  <AvatarFallback className="text-xs">
                                    {getTeamMemberName(memberId)
                                      .split(" ")
                                      .map((n) => n[0])
                                      .join("")}
                                  </AvatarFallback>
                                </Avatar>
                              ))}
                            {installation.assignedTeam.length > 3 && (
                              <div className="h-6 w-6 rounded-full bg-muted border-2 border-background flex items-center justify-center">
                                <span className="text-xs text-muted-foreground">
                                  +{installation.assignedTeam.length - 3}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Payment Status */}
                      <div className="flex items-center space-x-2">
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">
                          Payment:{" "}
                          {installation.paymentReleased ? (
                            <span className="text-green-600 font-medium">
                              Released
                            </span>
                          ) : (
                            <span className="text-yellow-600 font-medium">
                              Pending
                            </span>
                          )}
                        </span>
                      </div>
                    </div>

                    <div className="ml-6 flex flex-col space-y-2">
                      <Link href={`/merchant/installations/${installation.id}`}>
                        <Button variant="outline" size="sm">
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </Button>
                      </Link>
                      {installation.status === "scheduled" && (
                        <Link
                          href={`/merchant/installations/${installation.id}/edit`}
                        >
                          <Button variant="outline" size="sm">
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </Button>
                        </Link>
                      )}
                      {installation.status === "pending_approval" && (
                        <Button size="sm">
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Follow Up
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredInstallations.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <Calendar className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-muted-foreground mb-4">
              No installations found matching your filters.
            </p>
            <Link href="/merchant/installations/new">
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Schedule Your First Installation
              </Button>
            </Link>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
