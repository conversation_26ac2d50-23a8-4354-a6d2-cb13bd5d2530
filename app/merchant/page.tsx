"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Users,
  Package,
  Wrench,
  Calendar,
  TrendingUp,
  AlertCircle,
  Clock,
  Star,
  Eye,
} from "lucide-react";
import {
  MerchantDashboardStats,
  GroupPurchase,
  InstallationTask,
} from "@/types/merchant";
import { getStoneImagesForCategory } from "@/lib/stone-images";
import { formatZAR } from "@/data/south-african-context";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";

// Mock data - replace with actual API calls
const mockStats: MerchantDashboardStats = {
  totalGroups: 24,
  activeGroups: 18,
  readyForManufacturing: 5,
  inManufacturing: 8,
  pendingInstallation: 3,
  completedInstallations: 12,
  totalRevenue: 1875000, // R1.875M
  monthlyRevenue: 277500, // R277.5K
  averageGroupSize: 6.2,
  averageOrderValue: 42750, // R42.75K
};

// Enhanced mock data with real product images and details
type DashboardGroupPurchase = Omit<GroupPurchase, "productImage"> & {
  productName: string;
  productImage: string;
  category: string;
  estimatedCompletion?: Date;
};

const mockRecentGroups: DashboardGroupPurchase[] = [
  {
    id: "g1",
    productId: "p1",
    merchantId: "m1",
    groupName: "Memorial for John Smith",
    adminId: "u1",
    totalMembers: 8,
    targetAmount: 48000, // R48K
    currentAmount: 43200, // R43.2K
    paymentProgress: 90,
    status: "collecting",
    createdAt: new Date("2024-01-15"),
    productName: "Classic Granite Memorial Stone",
    productImage: getStoneImagesForCategory("traditional", 1)[0] || "",
    category: "traditional",
  },
  {
    id: "g2",
    productId: "p2",
    merchantId: "m1",
    groupName: "Family Heritage Stone",
    adminId: "u2",
    totalMembers: 12,
    targetAmount: 67500, // R67.5K
    currentAmount: 54000, // R54K
    paymentProgress: 80,
    status: "manufacturing",
    createdAt: new Date("2024-01-10"),
    manufacturingStarted: new Date("2024-01-20"),
    productName: "Modern Minimalist Memorial",
    productImage: getStoneImagesForCategory("modern", 1)[0] || "",
    category: "modern",
    estimatedCompletion: new Date("2024-02-20"),
  },
  {
    id: "g3",
    productId: "p3",
    merchantId: "m1",
    groupName: "Custom Engraved Memorial",
    adminId: "u3",
    totalMembers: 6,
    targetAmount: 78000, // R78K
    currentAmount: 70200, // R70.2K
    paymentProgress: 90,
    status: "manufacturing",
    createdAt: new Date("2024-01-08"),
    manufacturingStarted: new Date("2024-01-25"),
    productName: "Custom Engraved Memorial Stone",
    productImage: getStoneImagesForCategory("custom", 1)[0] || "",
    category: "custom",
    estimatedCompletion: new Date("2024-02-25"),
  },
  {
    id: "g4",
    productId: "p4",
    merchantId: "m1",
    groupName: "Premium Granite Monument",
    adminId: "u4",
    totalMembers: 15,
    targetAmount: 127500, // R127.5K
    currentAmount: 114750, // R114.75K
    paymentProgress: 90,
    status: "installing",
    createdAt: new Date("2024-01-05"),
    manufacturingStarted: new Date("2024-01-15"),
    manufacturingCompleted: new Date("2024-02-05"),
    productName: "Premium Granite Monument",
    productImage: getStoneImagesForCategory("premium", 1)[0] || "",
    category: "premium",
  },
];

type DashboardInstallationTask = InstallationTask & {
  productName: string;
  productImage: string;
  category: string;
  groupName: string;
};

const mockUpcomingInstallations: DashboardInstallationTask[] = [
  {
    id: "i1",
    groupId: "g3",
    productId: "p1",
    merchantId: "m1",
    title: "Memorial Installation - Westpark Cemetery",
    description: "Installation of granite memorial stone",
    installationAddress: "Westpark Cemetery, Johannesburg, Section B, Plot 45",
    scheduledDate: new Date("2024-02-15"),
    estimatedDuration: 4,
    assignedTeam: ["t1", "t2"],
    teamLeadId: "t1",
    status: "scheduled",
    paymentReleased: false,
    createdAt: new Date("2024-01-25"),
    updatedAt: new Date("2024-01-25"),
    productName: "Classic Granite Memorial Stone",
    productImage: getStoneImagesForCategory("traditional", 1)[0] || "",
    category: "traditional",
    groupName: "Memorial for John Smith",
  },
  {
    id: "i2",
    groupId: "g5",
    productId: "p2",
    merchantId: "m1",
    title: "Modern Memorial Installation - Braamfontein Cemetery",
    description: "Installation of modern minimalist memorial stone",
    installationAddress:
      "Braamfontein Cemetery, Johannesburg, Garden Section, Plot 12",
    scheduledDate: new Date("2024-02-18"),
    estimatedDuration: 3,
    assignedTeam: ["t1", "t3"],
    teamLeadId: "t1",
    status: "scheduled",
    paymentReleased: false,
    createdAt: new Date("2024-01-28"),
    updatedAt: new Date("2024-01-28"),
    productName: "Modern Minimalist Memorial",
    productImage: getStoneImagesForCategory("modern", 1)[0] || "",
    category: "modern",
    groupName: "Thompson Family Memorial",
  },
];

export default function MerchantDashboard() {
  const [stats] = useState<MerchantDashboardStats>(mockStats);
  const [recentGroups] = useState<DashboardGroupPurchase[]>(mockRecentGroups);
  const [upcomingInstallations] = useState<DashboardInstallationTask[]>(
    mockUpcomingInstallations
  );

  const getStatusColor = (status: GroupPurchase["status"]) => {
    switch (status) {
      case "discussing":
        return "bg-blue-100 text-blue-800";
      case "collecting":
        return "bg-yellow-100 text-yellow-800";
      case "manufacturing":
        return "bg-purple-100 text-purple-800";
      case "installing":
        return "bg-orange-100 text-orange-800";
      case "completed":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <MerchantPageHeader
        title="Dashboard"
        description="Overview of your merchant operations and performance"
      />

      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Groups</CardTitle>
            <Users className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalGroups}</div>
            <p className="text-xs text-muted-foreground">
              {stats.activeGroups} active groups
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Ready for Manufacturing
            </CardTitle>
            <AlertCircle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.readyForManufacturing}
            </div>
            <p className="text-xs text-muted-foreground">
              80%+ payment collected
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              In Manufacturing
            </CardTitle>
            <Wrench className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.inManufacturing}</div>
            <p className="text-xs text-muted-foreground">
              Currently in production
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Monthly Revenue
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatZAR(stats.monthlyRevenue)}
            </div>
            <p className="text-xs text-muted-foreground">
              {formatZAR(stats.totalRevenue)} total
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-8 md:grid-cols-2">
        {/* Recent Groups */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Groups</CardTitle>
            <CardDescription>
              Latest group purchases and their payment progress
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {recentGroups.map((group) => (
              <div
                key={group.id}
                className="flex items-center space-x-4 p-4 border rounded-lg hover:bg-accent/50 transition-colors"
              >
                {/* Product Image */}
                <div className="relative w-16 h-16 flex-shrink-0 overflow-hidden rounded-lg bg-muted">
                  <Image
                    src={group.productImage || "/placeholder-image.jpg"}
                    alt={group.productName || "Product image"}
                    fill
                    className="object-cover"
                  />
                </div>

                {/* Group Details */}
                <div className="flex-1 space-y-2">
                  <div className="flex items-start justify-between">
                    <div>
                      <p className="font-medium">{group.groupName}</p>
                      <p className="text-sm text-muted-foreground">
                        {group.productName}
                      </p>
                    </div>
                    <Link href={`/merchant/groups/${group.id}`}>
                      <Button variant="outline" size="sm">
                        <Eye className="mr-1 h-3 w-3" />
                        View
                      </Button>
                    </Link>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Badge className={getStatusColor(group.status)}>
                      {group.status}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {group.totalMembers} members
                    </span>
                    {group.status === "manufacturing" &&
                      group.estimatedCompletion && (
                        <span className="text-sm text-muted-foreground">
                          • Est.{" "}
                          {group.estimatedCompletion.toLocaleDateString(
                            "en-ZA"
                          )}
                        </span>
                      )}
                  </div>

                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span className="font-medium">
                        {formatZAR(group.currentAmount)}
                      </span>
                      <span className="text-muted-foreground">
                        {formatZAR(group.targetAmount)}
                      </span>
                    </div>
                    <Progress value={group.paymentProgress} className="h-2" />
                    <div className="text-xs text-muted-foreground">
                      {group.paymentProgress}% collected
                    </div>
                  </div>
                </div>
              </div>
            ))}
            <Link href="/merchant/groups">
              <Button variant="outline" className="w-full">
                View All Groups
              </Button>
            </Link>
          </CardContent>
        </Card>

        {/* Upcoming Installations */}
        <Card>
          <CardHeader>
            <CardTitle>Upcoming Installations</CardTitle>
            <CardDescription>
              Scheduled installation tasks and assignments
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {upcomingInstallations.map((installation) => (
              <div
                key={installation.id}
                className="flex items-center space-x-4 p-4 border rounded-lg hover:bg-accent/50 transition-colors"
              >
                {/* Product Image */}
                <div className="relative w-16 h-16 flex-shrink-0 overflow-hidden rounded-lg bg-muted">
                  <Image
                    src={installation.productImage || "/placeholder-image.jpg"}
                    alt={installation.productName || "Product image"}
                    fill
                    className="object-cover"
                  />
                </div>

                {/* Installation Details */}
                <div className="flex-1 space-y-2">
                  <div className="flex items-start justify-between">
                    <div>
                      <p className="font-medium">{installation.title}</p>
                      <p className="text-sm text-muted-foreground">
                        {installation.groupName}
                      </p>
                    </div>
                    <Link href={`/merchant/installations/${installation.id}`}>
                      <Button variant="outline" size="sm">
                        <Eye className="mr-1 h-3 w-3" />
                        View
                      </Button>
                    </Link>
                  </div>

                  <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>
                        {installation.scheduledDate.toLocaleDateString("en-ZA")}
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="h-4 w-4" />
                      <span>{installation.estimatedDuration} hours</span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Badge
                      variant="outline"
                      className="text-blue-600 border-blue-600"
                    >
                      {installation.status}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {installation.productName}
                    </span>
                  </div>
                </div>
              </div>
            ))}
            <Link href="/merchant/installations">
              <Button variant="outline" className="w-full">
                View All Installations
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>

      {/* Featured Products Showcase */}
      <Card>
        <CardHeader>
          <CardTitle>Featured Products</CardTitle>
          <CardDescription>
            Your most popular memorial stones across all categories
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {[
              {
                category: "traditional",
                name: "Classic Granite Memorial",
                price: 19485, // R19,485
              },
              {
                category: "modern",
                name: "Modern Minimalist Memorial",
                price: 32985, // R32,985
              },
              {
                category: "custom",
                name: "Custom Engraved Memorial",
                price: 52485, // R52,485
              },
              {
                category: "premium",
                name: "Premium Granite Monument",
                price: 89985, // R89,985
              },
            ].map((product, index) => {
              const image = getStoneImagesForCategory(product.category, 1)[0];
              return (
                <div
                  key={index}
                  className="group relative overflow-hidden rounded-lg border bg-card hover:shadow-md transition-all"
                >
                  <div className="relative aspect-square overflow-hidden">
                    <Image
                      src={image}
                      alt={product.name}
                      fill
                      className="object-cover transition-transform group-hover:scale-105"
                    />
                    <div className="absolute top-2 right-2">
                      <Badge className="bg-yellow-500 text-yellow-900">
                        <Star className="w-3 h-3 mr-1 fill-current" />
                        Featured
                      </Badge>
                    </div>
                  </div>
                  <div className="p-4">
                    <h3 className="font-medium mb-1">{product.name}</h3>
                    <p className="text-sm text-muted-foreground mb-2 capitalize">
                      {product.category} Collection
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="font-semibold">
                        {formatZAR(product.price)}
                      </span>
                      <Link href="/merchant/catalog">
                        <Button variant="outline" size="sm">
                          Manage
                        </Button>
                      </Link>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common tasks and shortcuts</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <Link href="/merchant/inventory/new">
              <Button className="w-full" variant="outline">
                <Package className="mr-2 h-4 w-4" />
                Add New Product
              </Button>
            </Link>
            <Link href="/merchant/installations/new">
              <Button className="w-full" variant="outline">
                <Calendar className="mr-2 h-4 w-4" />
                Schedule Installation
              </Button>
            </Link>
            <Link href="/merchant/team">
              <Button className="w-full" variant="outline">
                <Users className="mr-2 h-4 w-4" />
                Manage Team
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
