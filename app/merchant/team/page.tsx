"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Search,
  Plus,
  Users,
  UserCheck,
  Mail,
  Phone,
  Calendar,
  Edit,
  Eye,
  Shield,
  AlertCircle,
  CheckCircle,
  Briefcase,
} from "lucide-react";
import { TeamMember } from "@/types/merchant";

// Mock data - replace with actual API calls
const mockTeamMembers: TeamMember[] = [
  {
    id: "t1",
    merchantId: "m1",
    name: "<PERSON>hab<PERSON> Mthembu",
    email: "<EMAIL>",
    phone: "+27 82 123 4567",
    photoUrl: "/inventory/stone-15.jpeg",
    role: "contractor",
    specialties: ["Stone Installation", "Foundation Work", "Heavy Equipment"],
    isActive: true,
    isVerified: true,
    bankingDetails: {
      accountNumber: "****1234",
      routingNumber: "632005",
      bankName: "Standard Bank",
      accountHolderName: "Thabo Mthembu",
    },
    identification: {
      idType: "drivers_license",
      idNumber: "DL123456789",
      idImageUrl: "/inventory/stone-16.jpeg",
    },
    joinedAt: new Date("2023-06-01"),
  },
  {
    id: "t2",
    merchantId: "m1",
    name: "Sarah van der Merwe",
    email: "<EMAIL>",
    phone: "+27 83 234 5678",
    photoUrl: "/inventory/stone-25.jpeg",
    role: "contractor",
    specialties: ["Monument Installation", "Landscaping", "Site Preparation"],
    isActive: true,
    isVerified: false,
    bankingDetails: {
      accountNumber: "****5678",
      routingNumber: "632005",
      bankName: "Standard Bank",
      accountHolderName: "Sarah van der Merwe",
    },
    identification: {
      idType: "drivers_license",
      idNumber: "DL987654321",
      idImageUrl: "/inventory/stone-26.jpeg",
    },
    joinedAt: new Date("2023-08-15"),
  },
  {
    id: "t3",
    merchantId: "m1",
    name: "Sipho Dlamini",
    email: "<EMAIL>",
    phone: "+27 84 345 6789",
    photoUrl: "/inventory/stone-35.jpeg",
    role: "employee",
    specialties: ["Equipment Operation", "Site Preparation", "Quality Control"],
    isActive: true,
    isVerified: true,
    joinedAt: new Date("2023-09-01"),
  },
  {
    id: "t4",
    merchantId: "m1",
    name: "Nomsa Khumalo",
    email: "<EMAIL>",
    phone: "+27 85 456 7890",
    photoUrl: "/inventory/stone-45.jpeg",
    role: "contractor",
    specialties: ["Engraving", "Detail Work", "Custom Installations"],
    isActive: true,
    isVerified: true,
    bankingDetails: {
      accountNumber: "****9012",
      routingNumber: "632005",
      bankName: "FNB",
      accountHolderName: "Nomsa Khumalo",
    },
    identification: {
      idType: "passport",
      idNumber: "P123456789",
      idImageUrl: "/inventory/stone-46.jpeg",
    },
    joinedAt: new Date("2023-10-20"),
  },
  {
    id: "t5",
    merchantId: "m1",
    name: "Pieter Botha",
    email: "<EMAIL>",
    phone: "+27 86 567 8901",
    photoUrl: "/inventory/stone-55.jpeg",
    role: "manager",
    specialties: ["Project Management", "Quality Assurance", "Team Leadership"],
    isActive: true,
    isVerified: true,
    joinedAt: new Date("2023-05-15"),
  },
  {
    id: "t6",
    merchantId: "m1",
    name: "Zanele Ndlovu",
    email: "<EMAIL>",
    phone: "+27 87 678 9012",
    role: "contractor",
    specialties: ["Memorial Restoration", "Cleaning", "Maintenance"],
    isActive: false,
    isVerified: false,
    bankingDetails: {
      accountNumber: "****3456",
      routingNumber: "632005",
      bankName: "ABSA Bank",
      accountHolderName: "Zanele Ndlovu",
    },
    joinedAt: new Date("2023-07-10"),
  },
];

export default function MerchantTeam() {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>(mockTeamMembers);
  const [filteredMembers, setFilteredMembers] =
    useState<TeamMember[]>(mockTeamMembers);
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");

  useEffect(() => {
    let filtered = teamMembers;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (member) =>
          member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          member.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          member.specialties.some((specialty) =>
            specialty.toLowerCase().includes(searchTerm.toLowerCase())
          )
      );
    }

    // Role filter
    if (roleFilter !== "all") {
      filtered = filtered.filter((member) => member.role === roleFilter);
    }

    // Status filter
    if (statusFilter !== "all") {
      if (statusFilter === "active") {
        filtered = filtered.filter((member) => member.isActive);
      } else if (statusFilter === "inactive") {
        filtered = filtered.filter((member) => !member.isActive);
      }
    }

    setFilteredMembers(filtered);
  }, [teamMembers, searchTerm, roleFilter, statusFilter]);

  const getRoleColor = (role: TeamMember["role"]) => {
    switch (role) {
      case "contractor":
        return "bg-blue-100 text-blue-800";
      case "employee":
        return "bg-green-100 text-green-800";
      case "manager":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getRoleIcon = (role: TeamMember["role"]) => {
    switch (role) {
      case "contractor":
        return <Briefcase className="h-4 w-4" />;
      case "employee":
        return <UserCheck className="h-4 w-4" />;
      case "manager":
        return <Shield className="h-4 w-4" />;
      default:
        return <Users className="h-4 w-4" />;
    }
  };

  const hasCompleteDocuments = (member: TeamMember) => {
    if (member.role === "contractor") {
      return member.bankingDetails && member.identification;
    }
    return true; // Employees don't need banking details
  };

  const isVerified = (member: TeamMember) => {
    return member.isVerified ?? false;
  };

  // Calculate summary statistics
  const activeMembers = filteredMembers.filter((m) => m.isActive).length;
  const contractors = filteredMembers.filter(
    (m) => m.role === "contractor"
  ).length;
  const employees = filteredMembers.filter((m) => m.role === "employee").length;
  const managers = filteredMembers.filter((m) => m.role === "manager").length;
  const incompleteDocuments = filteredMembers.filter(
    (m) => m.isActive && !hasCompleteDocuments(m)
  ).length;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Team</h1>
          <p className="text-muted-foreground">
            Manage your team members and contractors
          </p>
        </div>
        <Link href="/merchant/team/new">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Team Member
          </Button>
        </Link>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Active Members
            </CardTitle>
            <Users className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeMembers}</div>
            <p className="text-xs text-muted-foreground">
              of {filteredMembers.length} total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Contractors</CardTitle>
            <Briefcase className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{contractors}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Employees</CardTitle>
            <UserCheck className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{employees}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Managers</CardTitle>
            <Shield className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{managers}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Incomplete Docs
            </CardTitle>
            <AlertCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{incompleteDocuments}</div>
            <p className="text-xs text-muted-foreground">Missing documents</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search team members..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger className="w-full sm:w-[150px]">
                <SelectValue placeholder="Role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                <SelectItem value="contractor">Contractor</SelectItem>
                <SelectItem value="employee">Employee</SelectItem>
                <SelectItem value="manager">Manager</SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Team Members Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredMembers.map((member) => (
          <Card
            key={member.id}
            className={!member.isActive ? "opacity-60" : ""}
          >
            <CardHeader className="pb-4">
              <div className="flex items-center space-x-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={member.photoUrl} alt={member.name} />
                  <AvatarFallback>
                    {member.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <h3 className="font-semibold">{member.name}</h3>
                    {!member.isActive && (
                      <Badge variant="secondary">Inactive</Badge>
                    )}
                    {isVerified(member) ? (
                      <Badge className="bg-green-100 text-green-800">
                        <CheckCircle className="mr-1 h-3 w-3" />
                        Verified
                      </Badge>
                    ) : (
                      <Badge className="bg-yellow-100 text-yellow-800">
                        <AlertCircle className="mr-1 h-3 w-3" />
                        Unverified
                      </Badge>
                    )}
                  </div>
                  <Badge className={getRoleColor(member.role)}>
                    <div className="flex items-center space-x-1">
                      {getRoleIcon(member.role)}
                      <span className="capitalize">{member.role}</span>
                    </div>
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Contact Information */}
              <div className="space-y-2">
                <div className="flex items-center space-x-2 text-sm">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span className="truncate">{member.email}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span>{member.phone}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span>
                    Joined{" "}
                    {member.joinedAt.toLocaleDateString("en-ZA", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })}
                  </span>
                </div>
              </div>

              {/* Specialties */}
              <div>
                <h4 className="text-sm font-medium mb-2">Specialties</h4>
                <div className="flex flex-wrap gap-1">
                  {member.specialties.map((specialty, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {specialty}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Document Status for Contractors */}
              {member.role === "contractor" && (
                <div className="flex items-center space-x-2">
                  {hasCompleteDocuments(member) ? (
                    <>
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm text-green-600">
                        Documents Complete
                      </span>
                    </>
                  ) : (
                    <>
                      <AlertCircle className="h-4 w-4 text-red-500" />
                      <span className="text-sm text-red-600">
                        Missing Documents
                      </span>
                    </>
                  )}
                </div>
              )}

              {/* Actions */}
              <div className="flex gap-2 pt-2">
                <Link href={`/merchant/team/${member.id}`} className="flex-1">
                  <Button variant="outline" size="sm" className="w-full">
                    <Eye className="mr-2 h-4 w-4" />
                    View
                  </Button>
                </Link>
                <Link
                  href={`/merchant/team/${member.id}/edit`}
                  className="flex-1"
                >
                  <Button variant="outline" size="sm" className="w-full">
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredMembers.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <Users className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-muted-foreground mb-4">
              No team members found matching your filters.
            </p>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Your First Team Member
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
