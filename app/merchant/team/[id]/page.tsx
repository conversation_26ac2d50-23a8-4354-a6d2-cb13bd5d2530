"use client";

import { useState } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  ChevronLeft,
  Phone,
  Mail,
  Calendar,
  Briefcase,
  Shield,
  UserCheck,
  Edit,
  CheckCircle,
  AlertCircle,
  DollarSign,
  FileText,
  Download,
} from "lucide-react";
import { TeamMember, InstallationTask } from "@/types/merchant";
import { formatZAR } from "../../../../data/south-african-context";

// Mock data - replace with actual API calls
const mockTeamMember: TeamMember = {
  id: "t1",
  merchantId: "m1",
  name: "<PERSON>hab<PERSON> Mthembu",
  email: "<EMAIL>",
  phone: "+27 82 123 4567",
  photoUrl: "/inventory/stone-15.jpeg",
  role: "contractor",
  specialties: [
    "Stone Installation",
    "Foundation Work",
    "Heavy Equipment",
    "Site Preparation",
  ],
  isActive: true,
  isVerified: true,
  bankingDetails: {
    accountNumber: "****1234",
    routingNumber: "632005",
    bankName: "Standard Bank",
    accountHolderName: "Thabo Mthembu",
  },
  identification: {
    idType: "drivers_license",
    idNumber: "DL123456789",
    idImageUrl: "/inventory/stone-16.jpeg",
  },
  joinedAt: new Date("2023-06-01"),
};

const mockInstallationHistory: InstallationTask[] = [
  {
    id: "inst1",
    groupId: "g1",
    productId: "p1",
    merchantId: "m1",
    title: "Memorial Installation - Stellenbosch Cemetery",
    description:
      "Installation of granite memorial stone for the Mthembu family",
    installationAddress:
      "Stellenbosch Cemetery, Section C, Plot 23, Stellenbosch, Western Cape",
    scheduledDate: new Date("2024-02-15"),
    estimatedDuration: 4,
    assignedTeam: ["t1", "t2"],
    teamLeadId: "t1",
    status: "completed",
    paymentReleased: true,
    createdAt: new Date("2024-01-25"),
    updatedAt: new Date("2024-02-15"),
  },
  {
    id: "inst2",
    groupId: "g2",
    productId: "p2",
    merchantId: "m1",
    title: "Heritage Stone Installation - Paarl Memorial",
    description: "Installation of custom heritage memorial stone",
    installationAddress:
      "Paarl Memorial Park, Garden Section, Paarl, Western Cape",
    scheduledDate: new Date("2024-02-20"),
    estimatedDuration: 6,
    assignedTeam: ["t1", "t2", "t3"],
    teamLeadId: "t1",
    status: "scheduled",
    paymentReleased: false,
    createdAt: new Date("2024-01-28"),
    updatedAt: new Date("2024-01-28"),
  },
];

export default function TeamMemberDetails() {
  const params = useParams();
  const memberId = params.id as string;

  const [teamMember, setTeamMember] = useState<TeamMember>(mockTeamMember);
  const [installationHistory, setInstallationHistory] = useState<
    InstallationTask[]
  >(mockInstallationHistory);

  const getRoleColor = (role: TeamMember["role"]) => {
    switch (role) {
      case "contractor":
        return "bg-blue-100 text-blue-800";
      case "employee":
        return "bg-green-100 text-green-800";
      case "manager":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getRoleIcon = (role: TeamMember["role"]) => {
    switch (role) {
      case "contractor":
        return <Briefcase className="h-4 w-4" />;
      case "employee":
        return <UserCheck className="h-4 w-4" />;
      case "manager":
        return <Shield className="h-4 w-4" />;
      default:
        return <UserCheck className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: InstallationTask["status"]) => {
    switch (status) {
      case "scheduled":
        return "bg-blue-100 text-blue-800";
      case "in_progress":
        return "bg-yellow-100 text-yellow-800";
      case "completed":
        return "bg-green-100 text-green-800";
      case "pending_approval":
        return "bg-orange-100 text-orange-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const hasCompleteDocuments =
    teamMember.role === "contractor"
      ? teamMember.bankingDetails && teamMember.identification
      : true;

  const completedInstallations = installationHistory.filter(
    (i) => i.status === "completed"
  ).length;
  const totalEarnings =
    installationHistory.filter(
      (i) => i.status === "completed" && i.paymentReleased
    ).length * 500; // Mock calculation

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/merchant/team">
            <Button variant="outline" size="icon">
              <ChevronLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div className="flex items-center space-x-4">
            <Avatar className="h-16 w-16">
              <AvatarImage src={teamMember.photoUrl} alt={teamMember.name} />
              <AvatarFallback className="text-lg">
                {teamMember.name
                  .split(" ")
                  .map((n) => n[0])
                  .join("")}
              </AvatarFallback>
            </Avatar>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                {teamMember.name}
              </h1>
              <div className="flex items-center space-x-2">
                <Badge className={getRoleColor(teamMember.role)}>
                  <div className="flex items-center space-x-1">
                    {getRoleIcon(teamMember.role)}
                    <span className="capitalize">{teamMember.role}</span>
                  </div>
                </Badge>
                {!teamMember.isActive && (
                  <Badge variant="secondary">Inactive</Badge>
                )}
                {teamMember.isVerified ? (
                  <Badge className="bg-green-100 text-green-800">
                    <CheckCircle className="mr-1 h-3 w-3" />
                    Verified
                  </Badge>
                ) : (
                  <Badge className="bg-yellow-100 text-yellow-800">
                    <AlertCircle className="mr-1 h-3 w-3" />
                    Unverified
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="flex space-x-2">
          <Button variant="outline">
            <Phone className="mr-2 h-4 w-4" />
            Call
          </Button>
          <Button variant="outline">
            <Mail className="mr-2 h-4 w-4" />
            Email
          </Button>
          <Link href={`/merchant/team/${teamMember.id}/edit`}>
            <Button>
              <Edit className="mr-2 h-4 w-4" />
              Edit Profile
            </Button>
          </Link>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Installations</CardTitle>
            <Briefcase className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedInstallations}</div>
            <p className="text-xs text-muted-foreground">
              of {installationHistory.length} total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Earnings
            </CardTitle>
            <DollarSign className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatZAR(totalEarnings)}</div>
            <p className="text-xs text-muted-foreground">This year</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Experience</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.floor(
                (new Date().getTime() - teamMember.joinedAt.getTime()) /
                  (1000 * 60 * 60 * 24 * 30)
              )}
            </div>
            <p className="text-xs text-muted-foreground">months with us</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Specialties</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {teamMember.specialties.length}
            </div>
            <p className="text-xs text-muted-foreground">skill areas</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="profile" className="space-y-4">
        <TabsList>
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="installations">Installation History</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="payments">Payments</TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Email</p>
                    <p className="text-sm text-muted-foreground">
                      {teamMember.email}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Phone</p>
                    <p className="text-sm text-muted-foreground">
                      {teamMember.phone}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Joined</p>
                    <p className="text-sm text-muted-foreground">
                      {teamMember.joinedAt.toLocaleDateString("en-ZA", {
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                      })}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Specialties & Skills</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {teamMember.specialties.map((specialty, index) => (
                    <Badge key={index} variant="outline">
                      {specialty}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Performance Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">98%</p>
                  <p className="text-sm text-muted-foreground">
                    On-time completion
                  </p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-blue-600">4.9</p>
                  <p className="text-sm text-muted-foreground">
                    Customer rating
                  </p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-purple-600">15</p>
                  <p className="text-sm text-muted-foreground">Projects led</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-orange-600">0</p>
                  <p className="text-sm text-muted-foreground">
                    Safety incidents
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="installations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Installation History</CardTitle>
              <CardDescription>
                Past and upcoming installation assignments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {installationHistory.map((installation) => (
                  <div
                    key={installation.id}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="space-y-1">
                      <h4 className="font-medium">{installation.title}</h4>
                      <p className="text-sm text-muted-foreground">
                        {installation.description}
                      </p>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3" />
                          <span>
                            {installation.scheduledDate.toLocaleDateString(
                              "en-ZA",
                              {
                                year: "numeric",
                                month: "long",
                                day: "numeric",
                              }
                            )}
                          </span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <UserCheck className="h-3 w-3" />
                          <span>
                            {installation.teamLeadId === teamMember.id
                              ? "Team Lead"
                              : "Team Member"}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      <Badge className={getStatusColor(installation.status)}>
                        {installation.status.replace("_", " ")}
                      </Badge>
                      <Link href={`/merchant/installations/${installation.id}`}>
                        <Button variant="outline" size="sm">
                          View
                        </Button>
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-6">
          {teamMember.role === "contractor" && (
            <>
              <Card>
                <CardHeader>
                  <CardTitle>Banking Information</CardTitle>
                  <CardDescription>
                    Payment details for contractor compensation
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {teamMember.bankingDetails ? (
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Bank Name:</span>
                        <span className="text-sm text-muted-foreground">
                          {teamMember.bankingDetails.bankName}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">
                          Account Holder:
                        </span>
                        <span className="text-sm text-muted-foreground">
                          {teamMember.bankingDetails.accountHolderName}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">
                          Account Number:
                        </span>
                        <span className="text-sm text-muted-foreground">
                          {teamMember.bankingDetails.accountNumber}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">
                          Routing Number:
                        </span>
                        <span className="text-sm text-muted-foreground">
                          {teamMember.bankingDetails.routingNumber}
                        </span>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <AlertCircle className="mx-auto h-8 w-8 text-red-500 mb-2" />
                      <p className="text-sm text-muted-foreground">
                        Banking information not provided
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Identification</CardTitle>
                  <CardDescription>
                    Identity verification documents
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {teamMember.identification ? (
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">ID Type:</span>
                        <span className="text-sm text-muted-foreground capitalize">
                          {teamMember.identification.idType.replace("_", " ")}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">ID Number:</span>
                        <span className="text-sm text-muted-foreground">
                          {teamMember.identification.idNumber}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Document:</span>
                        <Button variant="outline" size="sm">
                          <Download className="mr-2 h-4 w-4" />
                          View Document
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <AlertCircle className="mx-auto h-8 w-8 text-red-500 mb-2" />
                      <p className="text-sm text-muted-foreground">
                        Identification not provided
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </>
          )}

          {teamMember.role !== "contractor" && (
            <Card>
              <CardHeader>
                <CardTitle>Employee Documents</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Employee documentation is managed through HR systems.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="payments" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Payment History</CardTitle>
              <CardDescription>
                Contractor payment records and earnings
              </CardDescription>
            </CardHeader>
            <CardContent>
              {teamMember.role === "contractor" ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-3 gap-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold">
                        {totalEarnings.toLocaleString("en-ZA", {
                          style: "currency",
                          currency: "ZAR",
                        })}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Total Earned
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold">
                        {(totalEarnings * 0.1).toLocaleString("en-ZA", {
                          style: "currency",
                          currency: "ZAR",
                        })}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        This Month
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold">
                        {(0).toLocaleString("en-ZA", {
                          style: "currency",
                          currency: "ZAR",
                        })}
                      </p>
                      <p className="text-sm text-muted-foreground">Pending</p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium">Recent Payments</h4>
                    <div className="space-y-2">
                      {installationHistory
                        .filter(
                          (i) => i.status === "completed" && i.paymentReleased
                        )
                        .map((installation) => (
                          <div
                            key={installation.id}
                            className="flex items-center justify-between p-3 border rounded"
                          >
                            <div>
                              <p className="text-sm font-medium">
                                {installation.title}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {installation.scheduledDate.toLocaleDateString(
                                  "en-ZA",
                                  {
                                    year: "numeric",
                                    month: "long",
                                    day: "numeric",
                                  }
                                )}
                              </p>
                            </div>
                            <div className="text-right">
                              <p className="text-sm font-medium">
                                {(9250).toLocaleString("en-ZA", {
                                  style: "currency",
                                  currency: "ZAR",
                                })}
                              </p>
                              <Badge className="bg-green-100 text-green-800 text-xs">
                                Paid
                              </Badge>
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">
                  Payment information is not applicable for employees.
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
