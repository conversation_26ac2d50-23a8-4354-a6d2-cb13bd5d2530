"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  Grid3X3,
  Eye,
  EyeOff,
  Star,
  Package,
  Settings,
  Monitor,
  Smartphone,
  ArrowUpDown,
  Plus,
  Edit,
  BarChart3,
} from "lucide-react";
import { ProductCategory, MerchantInventoryItem } from "@/types/merchant";
import { getMerchantCategories } from "@/data/merchant";
import { formatZAR, convertToZAR } from "@/data/south-african-context";
import { getStoneImagesForCategory } from "@/lib/stone-images";
import { MerchantPageHeader } from "@/components/merchant/merchant-page-header";

// South African inventory data with real stone images
const mockInventory: MerchantInventoryItem[] = [
  {
    id: "inv1",
    merchantId: "m1",
    name: "Classic Granite Memorial Stone",
    description:
      "Traditional granite memorial stone with elegant design. Crafted from locally sourced South African granite.",
    price: convertToZAR(1299.99),
    images: getStoneImagesForCategory("traditional", 3),
    category: "traditional",
    specifications: {},
    manufacturingTime: 21,
    manufacturingPhases: [],
    isActive: true,
    stockStatus: "made_to_order",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-15"),
  },
  {
    id: "inv2",
    merchantId: "m1",
    name: "Modern Minimalist Memorial",
    description:
      "Contemporary memorial stone with clean lines and modern aesthetic. Perfect for modern cemetery sections.",
    price: convertToZAR(2199.99),
    images: getStoneImagesForCategory("modern", 2),
    category: "modern",
    specifications: {},
    manufacturingTime: 28,
    manufacturingPhases: [],
    isActive: true,
    stockStatus: "made_to_order",
    createdAt: new Date("2024-01-05"),
    updatedAt: new Date("2024-01-20"),
  },
  {
    id: "inv3",
    merchantId: "m1",
    name: "Custom Engraved Memorial",
    description:
      "Personalized memorial stone with custom engraving and design options. Handcrafted by local artisans.",
    price: convertToZAR(3499.99),
    images: getStoneImagesForCategory("custom", 3),
    category: "custom",
    specifications: {},
    manufacturingTime: 42,
    manufacturingPhases: [],
    isActive: true,
    stockStatus: "made_to_order",
    createdAt: new Date("2023-12-15"),
    updatedAt: new Date("2024-01-10"),
  },
  {
    id: "inv4",
    merchantId: "m1",
    name: "Premium Granite Monument",
    description:
      "High-end granite monument with premium finishes and detailed craftsmanship.",
    price: convertToZAR(5999.99),
    images: getStoneImagesForCategory("premium", 2),
    category: "premium",
    specifications: {},
    manufacturingTime: 35,
    manufacturingPhases: [],
    isActive: true,
    stockStatus: "made_to_order",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-05"),
  },
  {
    id: "inv5",
    merchantId: "m1",
    name: "Compact Memorial Plaque",
    description:
      "Elegant compact memorial plaque perfect for smaller spaces and garden memorials.",
    price: convertToZAR(449.99),
    images: getStoneImagesForCategory("compact", 1),
    category: "compact",
    specifications: {},
    manufacturingTime: 14,
    manufacturingPhases: [],
    isActive: false,
    stockStatus: "out_of_stock",
    createdAt: new Date("2023-11-20"),
    updatedAt: new Date("2024-01-05"),
  },
];

export default function MerchantCatalog() {
  const [categories, setCategories] = useState<ProductCategory[]>([]);
  const [inventory, setInventory] =
    useState<MerchantInventoryItem[]>(mockInventory);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [viewMode, setViewMode] = useState<"desktop" | "mobile">("desktop");
  const [featuredProducts, setFeaturedProducts] = useState<string[]>([]);

  // Get categories for this merchant
  useEffect(() => {
    const merchantCategories = getMerchantCategories("m1");
    setCategories(merchantCategories);
  }, []);

  // Filter products based on search and category
  const filteredInventory = inventory.filter((item) => {
    const matchesSearch =
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory =
      selectedCategory === "all" || item.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  // Get category statistics
  const getCategoryStats = (categorySlug: string) => {
    const categoryProducts = inventory.filter(
      (item) => item.category === categorySlug
    );
    const activeProducts = categoryProducts.filter((item) => item.isActive);
    const avgPrice =
      categoryProducts.length > 0
        ? categoryProducts.reduce((sum, item) => sum + item.price, 0) /
          categoryProducts.length
        : 0;

    return {
      total: categoryProducts.length,
      active: activeProducts.length,
      avgPrice,
      featured: featuredProducts.filter((id) =>
        categoryProducts.some((p) => p.id === id)
      ).length,
    };
  };

  const toggleCategoryVisibility = (categoryId: string) => {
    setCategories(
      categories.map((cat) =>
        cat.id === categoryId ? { ...cat, isActive: !cat.isActive } : cat
      )
    );
  };

  const toggleProductFeatured = (productId: string) => {
    setFeaturedProducts((prev) =>
      prev.includes(productId)
        ? prev.filter((id) => id !== productId)
        : [...prev, productId]
    );
  };

  // Calculate overall statistics
  const totalProducts = inventory.length;
  const activeProducts = inventory.filter((item) => item.isActive).length;
  const totalCategories = categories.length;
  const activeCategories = categories.filter((cat) => cat.isActive).length;

  return (
    <div className="space-y-8">
      {/* Header */}
      <MerchantPageHeader
        title="Catalog Management"
        description="Organize how your products appear to customers"
        actions={
          <div className="flex items-center gap-2">
            <Link href="/merchant/categories">
              <Button variant="outline">
                <Settings className="mr-2 h-4 w-4" />
                Manage Categories
              </Button>
            </Link>
            <Link href="/merchant/inventory">
              <Button variant="outline">
                <Package className="mr-2 h-4 w-4" />
                Manage Products
              </Button>
            </Link>
          </div>
        }
      />

      {/* Statistics Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Products
            </CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalProducts}</div>
            <p className="text-xs text-muted-foreground">
              {activeProducts} active
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Categories</CardTitle>
            <Grid3X3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCategories}</div>
            <p className="text-xs text-muted-foreground">
              {activeCategories} visible
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Featured Products
            </CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{featuredProducts.length}</div>
            <p className="text-xs text-muted-foreground">Highlighted items</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Price</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatZAR(
                Math.round(
                  inventory.reduce((sum, item) => sum + item.price, 0) /
                    inventory.length || 0
                )
              )}
            </div>
            <p className="text-xs text-muted-foreground">Across all products</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="categories" className="space-y-6">
        <TabsList>
          <TabsTrigger value="categories">Category Organization</TabsTrigger>
          <TabsTrigger value="products">Product Presentation</TabsTrigger>
          <TabsTrigger value="preview">Customer Preview</TabsTrigger>
        </TabsList>

        <TabsContent value="categories" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Category Management</CardTitle>
              <CardDescription>
                Control how categories appear to customers and organize your
                product showcase
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {categories.map((category) => {
                  const stats = getCategoryStats(category.slug);
                  return (
                    <div
                      key={category.id}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div className="flex items-center space-x-4">
                        <div
                          className="w-4 h-4 rounded-full"
                          style={{ backgroundColor: category.color }}
                        />
                        <div>
                          <h3 className="font-medium">{category.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {stats.active} of {stats.total} products active •{" "}
                            {stats.featured} featured
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge
                          variant={category.isActive ? "default" : "secondary"}
                        >
                          {category.isActive ? "Visible" : "Hidden"}
                        </Badge>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => toggleCategoryVisibility(category.id)}
                        >
                          {category.isActive ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </Button>
                        <Link href={`/merchant/categories/${category.id}`}>
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </Link>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="products" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Product Presentation</CardTitle>
                  <CardDescription>
                    Manage featured products and how they appear to customers
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search products..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 w-64"
                    />
                  </div>
                  <Select
                    value={selectedCategory}
                    onValueChange={setSelectedCategory}
                  >
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.slug}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredInventory.map((product) => (
                  <div
                    key={product.id}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="flex items-center space-x-4">
                      <img
                        src={product.images[0]}
                        alt={product.name}
                        className="w-16 h-16 object-cover rounded-lg"
                      />
                      <div>
                        <h3 className="font-medium">{product.name}</h3>
                        <p className="text-sm text-muted-foreground">
                          {formatZAR(product.price)} • {product.category}
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge
                            variant={product.isActive ? "default" : "secondary"}
                          >
                            {product.isActive ? "Active" : "Inactive"}
                          </Badge>
                          {featuredProducts.includes(product.id) && (
                            <Badge
                              variant="outline"
                              className="text-yellow-600 border-yellow-600"
                            >
                              <Star className="w-3 h-3 mr-1 fill-current" />
                              Featured
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant={
                          featuredProducts.includes(product.id)
                            ? "default"
                            : "outline"
                        }
                        size="sm"
                        onClick={() => toggleProductFeatured(product.id)}
                      >
                        <Star className="h-4 w-4" />
                        {featuredProducts.includes(product.id)
                          ? "Unfeature"
                          : "Feature"}
                      </Button>
                      <Link href={`/merchant/inventory/${product.id}`}>
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                ))}
                {filteredInventory.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    No products found matching your criteria.
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preview" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Customer Preview</CardTitle>
                  <CardDescription>
                    See how your catalog appears to customers
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant={viewMode === "desktop" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setViewMode("desktop")}
                  >
                    <Monitor className="h-4 w-4 mr-2" />
                    Desktop
                  </Button>
                  <Button
                    variant={viewMode === "mobile" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setViewMode("mobile")}
                  >
                    <Smartphone className="h-4 w-4 mr-2" />
                    Mobile
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div
                className={`border rounded-lg p-6 ${
                  viewMode === "mobile" ? "max-w-sm mx-auto" : ""
                }`}
              >
                <div className="space-y-6">
                  <div>
                    <h2 className="text-xl font-semibold mb-4">
                      Product Categories
                    </h2>
                    <div
                      className={`grid gap-4 ${
                        viewMode === "mobile" ? "grid-cols-2" : "grid-cols-4"
                      }`}
                    >
                      {categories
                        .filter((cat) => cat.isActive)
                        .map((category) => {
                          const stats = getCategoryStats(category.slug);
                          return (
                            <div
                              key={category.id}
                              className="flex flex-col items-center rounded-lg border bg-card p-4 text-center"
                            >
                              <div
                                className="mb-2 flex h-12 w-12 items-center justify-center rounded-full"
                                style={{
                                  backgroundColor: `${category.color}20`,
                                  color: category.color,
                                }}
                              >
                                <Grid3X3 className="h-6 w-6" />
                              </div>
                              <p className="font-medium text-sm">
                                {category.name}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {stats.active} products
                              </p>
                            </div>
                          );
                        })}
                    </div>
                  </div>

                  {featuredProducts.length > 0 && (
                    <div>
                      <h2 className="text-xl font-semibold mb-4">
                        Featured Products
                      </h2>
                      <div
                        className={`grid gap-4 ${
                          viewMode === "mobile" ? "grid-cols-1" : "grid-cols-3"
                        }`}
                      >
                        {inventory
                          .filter(
                            (product) =>
                              featuredProducts.includes(product.id) &&
                              product.isActive
                          )
                          .slice(0, viewMode === "mobile" ? 2 : 3)
                          .map((product) => (
                            <div
                              key={product.id}
                              className="border rounded-lg overflow-hidden"
                            >
                              <div className="aspect-video relative">
                                <img
                                  src={product.images[0]}
                                  alt={product.name}
                                  className="object-cover w-full h-full"
                                />
                                <div className="absolute top-2 right-2">
                                  <Badge className="bg-yellow-500 text-yellow-900">
                                    <Star className="w-3 h-3 mr-1 fill-current" />
                                    Featured
                                  </Badge>
                                </div>
                              </div>
                              <div className="p-4">
                                <h3 className="font-medium mb-1">
                                  {product.name}
                                </h3>
                                <p className="text-sm text-muted-foreground mb-2">
                                  {product.description}
                                </p>
                                <p className="font-semibold">
                                  ${product.price.toFixed(2)}
                                </p>
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
