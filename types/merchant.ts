// Merchant System Types

export interface ProductCategory {
  id: string;
  merchantId: string;
  name: string;
  description: string;
  slug: string;
  color: string;
  icon?: string;
  isActive: boolean;
  productCount: number;
  displayOrder: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Merchant {
  id: string;
  name: string;
  email: string;
  phone: string;
  logo: string;
  rating: number;
  totalProducts: number;
  totalSales: number;
  joinedAt: Date;
  status: "active" | "pending" | "suspended";
  businessDetails: {
    businessName: string;
    businessAddress: string;
    taxId: string;
    businessType: string;
  };
}

export interface GlobalManufacturingPhase {
  id: string;
  merchantId: string;
  name: string;
  description: string;
  estimatedDuration: number; // in days
  order: number;
  isRequired: boolean;
  isGlobal: true;
  createdAt: Date;
  updatedAt: Date;
}

export interface ManufacturingPhase {
  id: string;
  productId?: string; // Optional for global phases
  merchantId: string;
  name: string;
  description: string;
  estimatedDuration: number; // in days
  order: number;
  isRequired: boolean;
  isGlobal: boolean; // true for global phases, false for product-specific
  globalPhaseId?: string; // Reference to global phase if this is derived from one
  createdAt: Date;
  updatedAt: Date;
}

export interface ManufacturingUpdate {
  id: string;
  groupId: string;
  phaseId: string;
  merchantId: string;
  title: string;
  description: string;
  images: string[];
  videos?: string[];
  status: "in_progress" | "completed" | "delayed" | "on_hold";
  completionPercentage: number;
  estimatedCompletion?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface GroupPurchase {
  id: string;
  productId: string;
  merchantId: string;
  groupName: string;
  adminId: string;
  totalMembers: number;
  targetAmount: number;
  currentAmount: number;
  paymentProgress: number; // percentage
  status:
    | "discussing"
    | "collecting"
    | "manufacturing"
    | "installing"
    | "completed"
    | "ready_for_installation";
  createdAt: Date;
  targetDate?: Date;
  manufacturingStarted?: Date;
  manufacturingCompleted?: Date;
  productImage?: string;
  productName?: string;
  estimatedCompletion?: Date;
}

export interface TeamMember {
  id: string;
  merchantId: string;
  name: string;
  email: string;
  phone: string;
  photoUrl?: string;
  role: "contractor" | "employee" | "manager";
  specialties: string[];
  isActive: boolean;
  isVerified?: boolean;
  bankingDetails?: {
    accountNumber: string;
    routingNumber: string;
    bankName: string;
    accountHolderName: string;
  };
  identification?: {
    idType: "drivers_license" | "passport" | "national_id";
    idNumber: string;
    idImageUrl: string;
  };
  joinedAt: Date;
}

export interface InstallationTask {
  id: string;
  groupId: string;
  productId: string;
  merchantId: string;
  title: string;
  description: string;
  installationAddress: string;
  scheduledDate: Date;
  estimatedDuration: number; // in hours
  assignedTeam: string[]; // TeamMember IDs
  teamLeadId: string;
  status:
    | "scheduled"
    | "in_progress"
    | "completed"
    | "cancelled"
    | "pending_approval";
  completionImages?: string[];
  completionNotes?: string;
  customerApproval?: {
    approved: boolean;
    approvedBy: string;
    approvedAt: Date;
    feedback?: string;
  };
  paymentReleased: boolean;
  createdAt: Date;
  updatedAt: Date;
  productImage?: string;
  productName?: string;
  groupName?: string;
}

export interface PaymentTracking {
  groupId: string;
  productId: string;
  merchantId: string;
  totalAmount: number;
  paidAmount: number;
  pendingAmount: number;
  paymentProgress: number;
  memberPayments: {
    userId: string;
    userName: string;
    shareAmount: number;
    paidAmount: number;
    paymentStatus: "pending" | "partial" | "completed";
    lastPaymentDate?: Date;
  }[];
  readyForManufacturing: boolean; // 80% threshold
  manufacturingThreshold: number; // default 80%
}

export interface MerchantInventoryItem {
  id: string;
  merchantId: string;
  name: string;
  description: string;
  price: number;
  images: string[];
  category: string;
  specifications: Record<string, string>;
  manufacturingTime: number; // in days
  manufacturingPhases: ManufacturingPhase[];
  isActive: boolean;
  stockStatus: "in_stock" | "made_to_order" | "out_of_stock";
  createdAt: Date;
  updatedAt: Date;
}

// Dashboard Statistics
export interface MerchantDashboardStats {
  totalGroups: number;
  activeGroups: number;
  readyForManufacturing: number;
  inManufacturing: number;
  pendingInstallation: number;
  completedInstallations: number;
  totalRevenue: number;
  monthlyRevenue: number;
  averageGroupSize: number;
  averageOrderValue: number;
}

// Filter and Search Types
export interface GroupFilter {
  status?: GroupPurchase["status"];
  paymentProgress?: "below_50" | "50_to_80" | "above_80" | "completed";
  dateRange?: {
    start: Date;
    end: Date;
  };
  productCategory?: string;
  sortBy?: "created_date" | "payment_progress" | "target_amount" | "group_size";
  sortOrder?: "asc" | "desc";
}

export interface InstallationFilter {
  status?: InstallationTask["status"];
  dateRange?: {
    start: Date;
    end: Date;
  };
  teamMember?: string;
  sortBy?: "scheduled_date" | "created_date" | "status";
  sortOrder?: "asc" | "desc";
}
